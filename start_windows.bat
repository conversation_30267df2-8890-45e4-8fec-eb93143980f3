@echo off
echo Forcefully terminating any old ngrok processes...
taskkill /F /IM ngrok.exe /T > nul 2>&1

echo Waiting 3 seconds for ngrok cloud session to clear...
timeout /t 3 /nobreak > nul

echo Installing dependencies from requirements.txt...
pip install -r "%~dp0requirements.txt"

echo Checking Node.js and LocalTunnel...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Warning: Node.js not found. LocalTunnel will not be available.
    echo Please install Node.js from https://nodejs.org if you want to use LocalTunnel.
) else (
    echo Node.js found. LocalTunnel will be auto-installed when needed.
)

echo Starting LMArena Proxy Server...
start "LMArena Proxy" cmd /c python "%~dp0proxy_server.py"

echo Waiting for server to start...
timeout /t 5 /nobreak > nul

echo Opening monitor dashboard and LMArena AI...
start "" "http://localhost:9080/monitor"
start "" "https://lmarena.ai"

exit