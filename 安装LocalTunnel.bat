@echo off
chcp 65001 >nul
echo ========================================
echo 安装LocalTunnel内网穿透工具
echo ========================================
echo.

echo [1/4] 检查Node.js安装状态...
node --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Node.js已安装
    node --version
) else (
    echo ❌ Node.js未安装
    echo.
    echo 正在下载并安装Node.js...
    echo 请稍等，这可能需要几分钟...
    
    REM 下载Node.js安装包
    powershell -Command "& {Invoke-WebRequest -Uri 'https://nodejs.org/dist/v20.11.0/node-v20.11.0-x64.msi' -OutFile 'nodejs_installer.msi'}"
    
    if exist nodejs_installer.msi (
        echo 开始安装Node.js...
        msiexec /i nodejs_installer.msi /quiet /norestart
        echo 安装完成，请重启命令行窗口
        del nodejs_installer.msi
        pause
        exit /b 0
    ) else (
        echo ❌ 下载失败，请手动安装Node.js
        echo 访问: https://nodejs.org/
        pause
        exit /b 1
    )
)

echo.
echo [2/4] 检查npm状态...
npm --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ npm已安装
    npm --version
) else (
    echo ❌ npm未找到，请重新安装Node.js
    pause
    exit /b 1
)

echo.
echo [3/4] 安装LocalTunnel...
echo 正在全局安装LocalTunnel...
npm install -g localtunnel
if %errorlevel% equ 0 (
    echo ✅ LocalTunnel安装成功
) else (
    echo ⚠️ 全局安装失败，尝试使用npx方式
    echo LocalTunnel将通过npx运行（无需全局安装）
)

echo.
echo [4/4] 测试LocalTunnel...
echo 测试LocalTunnel命令...
lt --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ LocalTunnel命令可用
    lt --version
) else (
    echo ℹ️ 将使用npx方式运行LocalTunnel
    npx localtunnel --version >nul 2>&1
    if %errorlevel% equ 0 (
        echo ✅ npx localtunnel可用
    ) else (
        echo ❌ LocalTunnel安装可能有问题
    )
)

echo.
echo ========================================
echo 安装完成！
echo ========================================
echo 现在您可以重新启动LMArena服务器
echo LocalTunnel将自动为您提供远程访问链接
echo ========================================
pause
