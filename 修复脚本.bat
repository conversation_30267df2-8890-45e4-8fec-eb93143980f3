@echo off
chcp 65001 >nul
echo ========================================
echo LMArena代理服务器自动修复脚本
echo ========================================
echo.

echo [1/6] 正在终止所有相关进程...
echo 终止ngrok进程...
taskkill /F /IM ngrok.exe /T >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ ngrok进程已终止
) else (
    echo ℹ️  未发现运行中的ngrok进程
)

echo 检查端口9080占用情况...
for /f "tokens=5" %%a in ('netstat -ano ^| findstr :9080') do (
    echo 发现进程 %%a 占用端口9080，正在终止...
    taskkill /F /PID %%a >nul 2>&1
    if %errorlevel% equ 0 (
        echo ✅ 进程 %%a 已终止
    )
)

echo.
echo [2/6] 等待ngrok云端会话清理...
echo 等待30秒让ngrok会话过期...
timeout /t 30 /nobreak >nul
echo ✅ 等待完成

echo.
echo [3/6] 检查配置文件...
if exist "logs\config.json" (
    echo ✅ 配置文件存在
) else (
    echo ⚠️  配置文件不存在，将使用默认配置
)

echo.
echo [4/6] 检查依赖项...
echo 检查Python...
python --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Python已安装
) else (
    echo ❌ Python未找到，请先安装Python
    pause
    exit /b 1
)

echo 检查依赖包...
pip show fastapi >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ FastAPI已安装
) else (
    echo 📦 正在安装依赖包...
    pip install -r requirements.txt
)

echo.
echo [5/6] 创建备用配置...
if not exist "logs" mkdir logs

echo 创建LocalTunnel备用配置...
(
echo {
echo   "network": {
echo     "manual_ip": null,
echo     "port": 9080,
echo     "auto_detect_ip": true
echo   },
echo   "ngrok": {
echo     "enabled": false,
echo     "authtoken": "",
echo     "static_domain": ""
echo   },
echo   "localtunnel": {
echo     "enabled": true,
echo     "subdomain": "lmarena-proxy-%random%",
echo     "auto_install": true
echo   },
echo   "serveo": {
echo     "enabled": false,
echo     "subdomain": ""
echo   }
echo }
) > logs\config_backup.json

echo ✅ 备用配置已创建

echo.
echo [6/6] 启动选项...
echo 请选择启动方式：
echo 1. 使用原配置启动（推荐先尝试）
echo 2. 使用LocalTunnel备用配置启动
echo 3. 仅本地访问（禁用所有内网穿透）
echo 4. 更换端口到9081
echo 5. 退出
echo.

set /p choice=请输入选择 (1-5): 

if "%choice%"=="1" goto start_original
if "%choice%"=="2" goto start_localtunnel
if "%choice%"=="3" goto start_local_only
if "%choice%"=="4" goto start_different_port
if "%choice%"=="5" goto end

:start_original
echo.
echo 🚀 使用原配置启动服务器...
python proxy_server.py
goto end

:start_localtunnel
echo.
echo 🚀 使用LocalTunnel配置启动服务器...
copy logs\config_backup.json logs\config.json >nul
python proxy_server.py
goto end

:start_local_only
echo.
echo 🚀 仅本地访问模式启动...
(
echo {
echo   "network": {"port": 9080},
echo   "ngrok": {"enabled": false},
echo   "localtunnel": {"enabled": false},
echo   "serveo": {"enabled": false}
echo }
) > logs\config.json
python proxy_server.py
goto end

:start_different_port
echo.
echo 🚀 使用端口9081启动服务器...
(
echo {
echo   "network": {"port": 9081},
echo   "ngrok": {"enabled": false},
echo   "localtunnel": {"enabled": true, "subdomain": "lmarena-proxy-%random%"},
echo   "serveo": {"enabled": false}
echo }
) > logs\config.json
python proxy_server.py
goto end

:end
echo.
echo 修复脚本执行完成
echo 如果问题仍然存在，请查看"故障排除指南.md"获取更多帮助
pause
