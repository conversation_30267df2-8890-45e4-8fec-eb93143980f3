# 依赖说明文档

## 核心依赖

### Web 框架和服务器
- **fastapi>=0.104.0** - 现代、快速的 Web 框架
- **uvicorn[standard]>=0.24.0** - ASGI 服务器，包含标准扩展
- **starlette>=0.27.0** - FastAPI 的底层框架

### WebSocket 支持
- **websockets>=11.0** - WebSocket 协议实现

### HTTP 客户端
- **aiohttp>=3.9.0** - 异步 HTTP 客户端/服务器

### 监控和指标
- **prometheus-client>=0.19.0** - Prometheus 监控指标客户端

### 内网穿透
- **pyngrok>=7.0.0** - ngrok Python 客户端

### 数据处理
- **python-multipart>=0.0.6** - 多部分表单数据解析

## 内置模块（无需安装）

以下模块是 Python 标准库的一部分，无需额外安装：

- `asyncio` - 异步编程支持
- `json` - JSON 数据处理
- `logging` - 日志记录
- `uuid` - UUID 生成
- `re` - 正则表达式
- `time` - 时间处理
- `contextlib` - 上下文管理器
- `typing` - 类型提示
- `dataclasses` - 数据类
- `enum` - 枚举类型
- `signal` - 信号处理
- `os` - 操作系统接口
- `traceback` - 异常追踪
- `datetime` - 日期时间处理
- `collections` - 集合类型
- `threading` - 线程支持
- `pathlib` - 路径操作
- `gzip` - 压缩支持
- `shutil` - 文件操作
- `subprocess` - 子进程管理
- `sys` - 系统相关
- `socket` - 网络编程
- `platform` - 平台信息
- `urllib` - URL 处理

## 可选依赖

### LocalTunnel 支持
- **Node.js** - JavaScript 运行时
- **npm** - Node.js 包管理器
- **localtunnel** - LocalTunnel 客户端

安装方法：
```bash
# 安装 Node.js (访问 https://nodejs.org)
# 然后安装 LocalTunnel
npm install -g localtunnel
```

### 性能优化（可选）
- **uvloop>=0.19.0** - Unix 系统上的高性能事件循环
- **httptools>=0.6.0** - 高性能 HTTP 解析器

注意：这些优化包主要在 Linux/macOS 上有效，Windows 上可能不可用。

## 安装方法

### 自动安装（推荐）

**Windows:**
```bash
install_dependencies.bat
```

**Linux/macOS:**
```bash
chmod +x install_dependencies.sh
./install_dependencies.sh
```

### 手动安装

```bash
# 升级 pip
python -m pip install --upgrade pip

# 安装核心依赖
pip install -r requirements.txt

# 可选：安装 LocalTunnel
npm install -g localtunnel
```

## 版本兼容性

- **Python**: 3.8+
- **Node.js**: 14+ (可选，用于 LocalTunnel)

## 故障排除

### 常见问题

1. **pip 安装失败**
   - 升级 pip: `python -m pip install --upgrade pip`
   - 使用国内镜像: `pip install -i https://pypi.tuna.tsinghua.edu.cn/simple -r requirements.txt`

2. **权限错误**
   - Windows: 以管理员身份运行命令提示符
   - Linux/macOS: 使用 `sudo` 或虚拟环境

3. **网络问题**
   - 使用代理: `pip install --proxy http://proxy:port -r requirements.txt`
   - 离线安装: 下载 wheel 文件手动安装

### 虚拟环境（推荐）

```bash
# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Windows:
venv\Scripts\activate
# Linux/macOS:
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt
```
